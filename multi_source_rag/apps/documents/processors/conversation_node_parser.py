"""
Conversation-Aware Node Parser using LlamaIndex Native Capabilities

This module implements conversation-aware document parsing using LlamaIndex's
HierarchicalNodeParser and custom conversation detection logic.
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Sequence, Set
from collections import defaultdict

from llama_index.core.schema import Document, BaseNode, TextNode
from llama_index.core.node_parser import HierarchicalNodeParser, SentenceWindowNodeParser
from llama_index.core.node_parser.interface import NodeParser
from llama_index.core.utils import get_tqdm_iterable

logger = logging.getLogger(__name__)


class ConversationMetadata:
    """Helper class for conversation metadata extraction."""
    
    @staticmethod
    def extract_conversation_context(messages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract conversation context from messages."""
        if not messages:
            return {}
        
        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.get('timestamp', '0')))
        
        # Extract participants
        participants = list(set(msg.get('user_name', msg.get('user', '')) for msg in messages))
        
        # Extract time span
        start_time = datetime.fromtimestamp(float(sorted_messages[0]['timestamp']))
        end_time = datetime.fromtimestamp(float(sorted_messages[-1]['timestamp']))
        
        # Detect conversation type
        thread_messages = [msg for msg in messages if msg.get('thread_ts')]
        has_questions = any('?' in msg.get('text', '') for msg in messages)
        has_reactions = any(msg.get('reactions') for msg in messages)
        
        conversation_type = 'thread' if thread_messages else 'discussion'
        if has_questions:
            conversation_type = 'q_and_a'
        
        # Calculate engagement score
        engagement_score = min(1.0, (
            len(participants) * 0.2 +
            (1.0 if has_questions else 0.0) * 0.3 +
            (1.0 if has_reactions else 0.0) * 0.2 +
            min(len(messages) / 10.0, 1.0) * 0.3
        ))
        
        return {
            'conversation_type': conversation_type,
            'participants': participants,
            'participant_count': len(participants),
            'message_count': len(messages),
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_minutes': (end_time - start_time).total_seconds() / 60,
            'has_threads': bool(thread_messages),
            'thread_count': len(set(msg.get('thread_ts') for msg in thread_messages)),
            'has_questions': has_questions,
            'has_reactions': has_reactions,
            'engagement_score': engagement_score
        }


class ConversationAwareNodeParser(NodeParser):
    """
    LlamaIndex-native conversation-aware node parser.
    
    Uses HierarchicalNodeParser for intelligent chunking while adding
    conversation-specific metadata and context preservation.
    """
    
    def __init__(
        self,
        chunk_size: int = 2048,
        chunk_overlap: int = 200,
        max_conversation_gap_minutes: int = 30,
        min_conversation_messages: int = 2,
        preserve_thread_boundaries: bool = True,
        include_prev_next_rel: bool = True,
        **kwargs
    ):
        """
        Initialize conversation-aware node parser.
        
        Args:
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between chunks
            max_conversation_gap_minutes: Max time gap to consider messages related
            min_conversation_messages: Minimum messages for a conversation
            preserve_thread_boundaries: Whether to preserve thread boundaries
            include_prev_next_rel: Whether to include previous/next relationships
        """
        super().__init__(**kwargs)
        
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_conversation_gap_minutes = max_conversation_gap_minutes
        self.min_conversation_messages = min_conversation_messages
        self.preserve_thread_boundaries = preserve_thread_boundaries
        
        # Initialize LlamaIndex hierarchical parser
        self.hierarchical_parser = HierarchicalNodeParser.from_defaults(
            chunk_sizes=[chunk_size],
            chunk_overlap=chunk_overlap,
            include_prev_next_rel=include_prev_next_rel
        )
        
        # Initialize sentence window parser for fine-grained context
        self.sentence_parser = SentenceWindowNodeParser.from_defaults(
            window_size=3,
            window_metadata_key="window",
            original_text_metadata_key="original_text"
        )
        
        self.stats = {
            'documents_processed': 0,
            'conversations_detected': 0,
            'nodes_created': 0,
            'processing_time_seconds': 0.0
        }
    
    def _parse_nodes(
        self, 
        nodes: Sequence[BaseNode], 
        show_progress: bool = False,
        **kwargs
    ) -> List[BaseNode]:
        """Parse nodes with conversation awareness."""
        start_time = datetime.now()
        
        parsed_nodes = []
        nodes_with_progress = get_tqdm_iterable(
            nodes, show_progress, "Parsing nodes with conversation awareness"
        )
        
        for node in nodes_with_progress:
            if isinstance(node, Document):
                # Process document with conversation awareness
                conversation_nodes = self._process_document_with_conversations(node)
                parsed_nodes.extend(conversation_nodes)
                self.stats['documents_processed'] += 1
            else:
                # Pass through non-document nodes
                parsed_nodes.append(node)
        
        # Update statistics
        processing_time = (datetime.now() - start_time).total_seconds()
        self.stats['processing_time_seconds'] = processing_time
        self.stats['nodes_created'] = len(parsed_nodes)
        
        logger.info(f"Conversation-aware parsing completed: {len(parsed_nodes)} nodes created in {processing_time:.2f}s")
        return parsed_nodes
    
    def _process_document_with_conversations(self, document: Document) -> List[BaseNode]:
        """Process a document with conversation awareness."""
        # Check if this is a Slack document with conversation structure
        if self._is_slack_conversation_document(document):
            return self._parse_slack_conversation(document)
        else:
            # Use standard hierarchical parsing for non-conversation documents
            return self.hierarchical_parser._parse_nodes([document])
    
    def _is_slack_conversation_document(self, document: Document) -> bool:
        """Check if document contains Slack conversation data."""
        metadata = document.metadata or {}
        content_type = metadata.get('content_type', '')
        source_type = metadata.get('source_type', '')
        
        return (
            'slack' in content_type.lower() or 
            'slack' in source_type.lower() or
            self._has_conversation_structure(document.text)
        )
    
    def _has_conversation_structure(self, text: str) -> bool:
        """Detect if text has conversation structure."""
        # Look for timestamp and user patterns typical in Slack exports
        patterns = [
            r'\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]',  # [YYYY-MM-DD HH:MM:SS]
            r'<@[A-Z0-9]+>',  # User mentions
            r':\w+:',  # Emoji reactions
        ]
        
        for pattern in patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def _parse_slack_conversation(self, document: Document) -> List[BaseNode]:
        """Parse Slack conversation document with conversation awareness."""
        # Extract messages from document text
        messages = self._extract_messages_from_text(document.text)
        
        if not messages:
            # Fallback to standard parsing
            return self.hierarchical_parser._parse_nodes([document])
        
        # Group messages into conversations
        conversations = self._group_messages_into_conversations(messages)
        
        # Create nodes for each conversation
        nodes = []
        for i, conversation in enumerate(conversations):
            conversation_node = self._create_conversation_node(
                conversation, document, i
            )
            if conversation_node:
                nodes.append(conversation_node)
        
        self.stats['conversations_detected'] += len(conversations)
        return nodes
    
    def _extract_messages_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract individual messages from formatted text."""
        messages = []
        
        # Split by double newlines to get individual messages
        message_blocks = text.split('\n\n')
        
        for block in message_blocks:
            if not block.strip():
                continue
                
            # Parse message format: [timestamp] user: content
            match = re.match(
                r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]\s*([^:]+):\s*(.*)',
                block.strip(),
                re.DOTALL
            )
            
            if match:
                timestamp_str, user, content = match.groups()
                
                # Convert timestamp to unix timestamp
                try:
                    dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    timestamp = str(dt.timestamp())
                except ValueError:
                    timestamp = '0'
                
                # Extract reactions and files if present
                reactions = re.findall(r':\w+:', content)
                files = re.findall(r'\[Files: ([^\]]+)\]', content)
                
                # Clean content
                clean_content = re.sub(r'\[:\w+:\]', '', content)  # Remove reactions
                clean_content = re.sub(r'\[Files: [^\]]+\]', '', clean_content)  # Remove files
                clean_content = clean_content.strip()
                
                messages.append({
                    'timestamp': timestamp,
                    'user_name': user.strip(),
                    'text': clean_content,
                    'reactions': reactions,
                    'files': files,
                    'original_block': block
                })
        
        return messages
    
    def _group_messages_into_conversations(self, messages: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Group messages into conversation clusters."""
        if not messages:
            return []
        
        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x['timestamp']))
        
        conversations = []
        current_conversation = [sorted_messages[0]]
        
        for i in range(1, len(sorted_messages)):
            prev_msg = sorted_messages[i-1]
            curr_msg = sorted_messages[i]
            
            # Calculate time gap
            time_gap = float(curr_msg['timestamp']) - float(prev_msg['timestamp'])
            time_gap_minutes = time_gap / 60
            
            # Check if messages should be in same conversation
            if time_gap_minutes <= self.max_conversation_gap_minutes:
                current_conversation.append(curr_msg)
            else:
                # Start new conversation if current one meets minimum size
                if len(current_conversation) >= self.min_conversation_messages:
                    conversations.append(current_conversation)
                current_conversation = [curr_msg]
        
        # Add the last conversation
        if len(current_conversation) >= self.min_conversation_messages:
            conversations.append(current_conversation)
        
        return conversations
    
    def _create_conversation_node(
        self, 
        conversation: List[Dict[str, Any]], 
        original_document: Document, 
        conversation_index: int
    ) -> Optional[TextNode]:
        """Create a TextNode for a conversation."""
        if not conversation:
            return None
        
        # Reconstruct conversation text
        conversation_text = '\n\n'.join(msg['original_block'] for msg in conversation)
        
        # Extract conversation metadata
        conversation_metadata = ConversationMetadata.extract_conversation_context(conversation)
        
        # Combine with original document metadata
        node_metadata = {
            **original_document.metadata,
            **conversation_metadata,
            'conversation_index': conversation_index,
            'chunking_strategy': 'conversation_aware',
            'parser_type': 'ConversationAwareNodeParser'
        }
        
        # Create node ID
        doc_id = original_document.metadata.get('document_id', 'unknown')
        node_id = f"{doc_id}_conv_{conversation_index}"
        
        # Create TextNode
        node = TextNode(
            text=conversation_text,
            metadata=node_metadata,
            id_=node_id
        )
        
        return node
    
    def get_stats(self) -> Dict[str, Any]:
        """Get parsing statistics."""
        return self.stats.copy()
